# 🔧 Guide de Dépannage - Erreur de Connexion Base de Données

## Erreur: "Erreur de connexion à la base de données. Veuillez contacter l'administrateur."

### ✅ Solutions Rapides (dans l'ordre)

#### 1. **Vérifier XAMPP**
- Ouvrez le panneau de contrôle XAMPP
- Vérifiez que **MySQL** est démarré (bouton "Start")
- Vérifiez que **Apache** est démarré (bouton "Start")
- Si MySQL ne démarre pas, changez le port ou redémarrez XAMPP

#### 2. **Installation Automatique** ⭐ **RECOMMANDÉ**
```
http://localhost/STAGE/install.php
```
- Ouvrez cette URL dans votre navigateur
- Cliquez sur "Installer la base de données"
- Suivez les instructions

#### 3. **Installation Manuelle via phpMyAdmin**
- Allez sur `http://localhost/phpmyadmin`
- Créez une nouvelle base de données nommée `reporting_tsp`
- Importez le fichier `setup.sql`

#### 4. **Installation en Ligne de Commande**
```bash
cd c:\xampp\htdocs\STAGE
php create_database.php
```

### 🔍 Diagnostic Avancé

#### Tester la Connexion
```bash
php test_connection.php
```
Ou ouvrez: `http://localhost/STAGE/test_connection.php`

#### Diagnostic Complet
```bash
php database_diagnostic.php
```

### 📋 Vérifications Système

#### XAMPP MySQL
1. Ouvrez XAMPP Control Panel
2. Cliquez sur "Admin" à côté de MySQL → doit ouvrir phpMyAdmin
3. Si erreur, redémarrez MySQL dans XAMPP

#### Ports
- MySQL utilise le port **3306** par défaut
- Si conflit, changez le port dans XAMPP Config

#### Permissions
- XAMPP doit être exécuté en tant qu'administrateur
- Vérifiez que le dossier `c:\xampp\htdocs\STAGE` est accessible

### 🚨 Messages d'Erreur Courants

#### "Connection refused" ou "Can't connect"
**Cause:** MySQL n'est pas démarré
**Solution:** Démarrez MySQL dans XAMPP

#### "Access denied for user 'root'"
**Cause:** Problème de permissions
**Solution:** 
1. Réinitialisez le mot de passe root MySQL
2. Ou modifiez `database.php` avec les bons identifiants

#### "Unknown database 'reporting_tsp'"
**Cause:** Base de données pas créée
**Solution:** Utilisez `install.php` ou importez `setup.sql`

### 📞 Support

Si le problème persiste:

1. **Vérifiez les logs d'erreur:**
   - `c:\xampp\mysql\data\mysql_error.log`
   - `c:\xampp\apache\logs\error.log`

2. **Informations système:**
   - Version de XAMPP
   - Version de PHP: `php -v`
   - Version de MySQL dans XAMPP

3. **Test minimal:**
   ```php
   <?php
   try {
       $pdo = new PDO("mysql:host=localhost", "root", "");
       echo "MySQL fonctionne!";
   } catch(Exception $e) {
       echo "Erreur: " . $e->getMessage();
   }
   ?>
   ```

### 🎯 Identifiants par Défaut

Après installation réussie:
- **Email:** <EMAIL>
- **Mot de passe:** admin123

### 📁 Fichiers Utiles

- `install.php` - Installation automatique (interface web)
- `create_database.php` - Installation en ligne de commande
- `test_connection.php` - Test de connexion
- `database_diagnostic.php` - Diagnostic complet
- `setup.sql` - Script SQL manuel
