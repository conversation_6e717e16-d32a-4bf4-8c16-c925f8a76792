# Identifiants de Connexion - Reporting TSP

## Compte Administrateur par Défaut

**Email :** `<EMAIL>`  
**Mot de passe :** `admin123`

## Instructions

### Pour une Nouvelle Installation
1. Exécutez le fichier `setup.sql` pour créer la base de données
2. Utilisez les identifiants ci-dessus pour vous connecter

### Pour une Installation Existante
1. Exécutez le script de réinitialisation :
   ```bash
   php reset_admin_password.php
   ```
2. Le script affichera les nouveaux identifiants
3. Supprimez le fichier `reset_admin_password.php` après utilisation

## Sécurité

⚠️ **IMPORTANT :**
- Changez le mot de passe par défaut après la première connexion
- Supprimez ce fichier après avoir noté les identifiants
- Ne partagez jamais ces identifiants publiquement

## Modification du Mot de Passe

Pour changer le mot de passe administrateur :
1. Modifiez la variable `$nouveau_mot_de_passe` dans `reset_admin_password.php`
2. Exécutez le script
3. Supprimez le fichier après utilisation

## Hash du Mot de Passe

Le hash actuel dans la base de données correspond au mot de passe `admin123` :
```
$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi
```
