<?php
/**
 * Script de test simple pour la connexion à la base de données
 */

echo "<h2>Test de Connexion - Base de Données TSP</h2>";

// Inclure le fichier de configuration
try {
    require_once 'database.php';
    echo "<p style='color: green;'>✓ Fichier database.php chargé avec succès</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur lors du chargement de database.php: " . $e->getMessage() . "</p>";
    exit;
}

// Tester la variable $pdo
if (isset($pdo)) {
    echo "<p style='color: green;'>✓ Variable \$pdo définie</p>";
    
    // Tester une requête simple
    try {
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✓ Requête de test réussie: " . $result['test'] . "</p>";
        
        // Tester les tables
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM utilisateurs");
            $users = $stmt->fetch();
            echo "<p style='color: green;'>✓ Table utilisateurs accessible - Nombre d'utilisateurs: " . $users['count'] . "</p>";
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM qualite_tsp");
            $data = $stmt->fetch();
            echo "<p style='color: green;'>✓ Table qualite_tsp accessible - Nombre d'enregistrements: " . $data['count'] . "</p>";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Erreur d'accès aux tables: " . $e->getMessage() . "</p>";
            echo "<p><strong>Solution:</strong> Exécutez le fichier setup.sql</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Erreur de requête: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Variable \$pdo non définie</p>";
}

// Informations sur la configuration
echo "<h3>Configuration Actuelle:</h3>";
echo "<ul>";
echo "<li><strong>Host:</strong> localhost</li>";
echo "<li><strong>Database:</strong> reporting_tsp</li>";
echo "<li><strong>Username:</strong> root</li>";
echo "<li><strong>Password:</strong> (vide)</li>";
echo "</ul>";

echo "<h3>Actions de Dépannage:</h3>";
echo "<ol>";
echo "<li>Vérifiez que XAMPP MySQL est démarré</li>";
echo "<li>Exécutez <code>php database_diagnostic.php</code> en ligne de commande</li>";
echo "<li>Importez le fichier setup.sql dans phpMyAdmin</li>";
echo "<li>Vérifiez les logs d'erreur PHP</li>";
echo "</ol>";
?>
