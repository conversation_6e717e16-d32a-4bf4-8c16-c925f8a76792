<?php
/**
 * Script de diagnostic pour la connexion à la base de données
 * Ce script teste étape par étape la connexion et identifie les problèmes
 */

echo "=== DIAGNOSTIC DE LA BASE DE DONNÉES TSP ===\n\n";

// 1. Vérifier les paramètres de connexion
echo "1. Vérification des paramètres de connexion...\n";
$host = 'localhost';
$dbname = 'reporting_tsp';
$username = 'root';
$password = '';

echo "   Host: $host\n";
echo "   Database: $dbname\n";
echo "   Username: $username\n";
echo "   Password: " . (empty($password) ? "vide" : "défini") . "\n\n";

// 2. Tester la connexion MySQL de base
echo "2. Test de connexion MySQL de base...\n";
try {
    $pdo_test = new PDO("mysql:host=$host", $username, $password);
    echo "   ✓ Connexion MySQL réussie\n\n";
} catch (PDOException $e) {
    echo "   ❌ Échec de connexion MySQL: " . $e->getMessage() . "\n";
    echo "   SOLUTION: Vérifiez que XAMPP MySQL est démarré\n\n";
    exit(1);
}

// 3. Vérifier l'existence de la base de données
echo "3. Vérification de l'existence de la base de données '$dbname'...\n";
try {
    $stmt = $pdo_test->query("SHOW DATABASES LIKE '$dbname'");
    $db_exists = $stmt->fetch();
    
    if ($db_exists) {
        echo "   ✓ Base de données '$dbname' trouvée\n\n";
    } else {
        echo "   ❌ Base de données '$dbname' n'existe pas\n";
        echo "   SOLUTION: Exécutez le fichier setup.sql\n\n";
        
        // Proposer de créer la base de données
        echo "4. Création automatique de la base de données...\n";
        try {
            $pdo_test->exec("CREATE DATABASE IF NOT EXISTS $dbname");
            echo "   ✓ Base de données '$dbname' créée\n\n";
        } catch (PDOException $e) {
            echo "   ❌ Impossible de créer la base de données: " . $e->getMessage() . "\n\n";
            exit(1);
        }
    }
} catch (PDOException $e) {
    echo "   ❌ Erreur lors de la vérification: " . $e->getMessage() . "\n\n";
    exit(1);
}

// 4. Tester la connexion à la base de données spécifique
echo "4. Test de connexion à la base de données '$dbname'...\n";
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Connexion à '$dbname' réussie\n\n";
} catch (PDOException $e) {
    echo "   ❌ Échec de connexion à '$dbname': " . $e->getMessage() . "\n\n";
    exit(1);
}

// 5. Vérifier l'existence des tables
echo "5. Vérification des tables...\n";
$tables_requises = ['utilisateurs', 'qualite_tsp'];
$tables_manquantes = [];

foreach ($tables_requises as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "   ✓ Table '$table' trouvée\n";
        } else {
            echo "   ❌ Table '$table' manquante\n";
            $tables_manquantes[] = $table;
        }
    } catch (PDOException $e) {
        echo "   ❌ Erreur lors de la vérification de '$table': " . $e->getMessage() . "\n";
        $tables_manquantes[] = $table;
    }
}

if (!empty($tables_manquantes)) {
    echo "\n   SOLUTION: Exécutez le fichier setup.sql pour créer les tables manquantes\n";
} else {
    echo "\n   ✓ Toutes les tables requises sont présentes\n";
}

// 6. Tester une requête simple
echo "\n6. Test d'une requête simple...\n";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM utilisateurs");
    $result = $stmt->fetch();
    echo "   ✓ Requête réussie - Nombre d'utilisateurs: " . $result['count'] . "\n";
} catch (PDOException $e) {
    echo "   ❌ Échec de la requête: " . $e->getMessage() . "\n";
}

// 7. Résumé et recommandations
echo "\n=== RÉSUMÉ ET RECOMMANDATIONS ===\n";

if (empty($tables_manquantes)) {
    echo "✓ La base de données semble correctement configurée\n";
    echo "✓ Vous devriez pouvoir vous connecter à l'application\n\n";
    
    echo "Si vous avez encore des problèmes:\n";
    echo "1. Redémarrez XAMPP (Apache et MySQL)\n";
    echo "2. Vérifiez les logs d'erreur PHP\n";
    echo "3. Testez avec le script test_connection.php\n";
} else {
    echo "❌ Configuration incomplète détectée\n";
    echo "📋 Actions requises:\n";
    echo "1. Exécutez: mysql -u root -p < setup.sql\n";
    echo "2. Ou importez setup.sql via phpMyAdmin\n";
    echo "3. Relancez ce diagnostic\n";
}

echo "\n=== FIN DU DIAGNOSTIC ===\n";
?>
