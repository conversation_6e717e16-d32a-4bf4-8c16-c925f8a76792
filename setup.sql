CREATE DATABASE IF NOT EXISTS reporting_tsp;
USE reporting_tsp;

CREATE TABLE utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL UNIQUE,
    mot_de_passe VARCHAR(255) NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email)
);

CREATE TABLE qualite_tsp (
    id INT AUTO_INCREMENT PRIMARY KEY,
    jour INT NOT NULL,
    p2o5_total DECIMAL(5,2) NOT NULL CHECK (p2o5_total >= 0 AND p2o5_total <= 100),
    p2o5_se DECIMAL(5,2) NOT NULL CHECK (p2o5_se >= 0 AND p2o5_se <= 100),
    h2o DECIMAL(5,2) NOT NULL CHECK (h2o >= 0 AND h2o <= 100),
    al2o3 DECIMAL(5,2) NOT NULL CHECK (al2o3 >= 0 AND al2o3 <= 100),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_jour (jour),
    INDEX idx_jour (jour),
    INDEX idx_date_creation (date_creation)
);

-- Utilisateur administrateur par défaut
-- Email: <EMAIL>
-- Mot de passe: admin123
INSERT INTO utilisateurs (email, mot_de_passe) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

INSERT INTO qualite_tsp (jour, p2o5_total, p2o5_se, h2o, al2o3) VALUES
(1, 44.5, 42.5, 5.5, 5.5),
(2, 44.6, 42.6, 5.2, 5.3),
(3, 44.7, 42.7, 4.9, 5.1),
(4, 44.8, 42.8, 4.5, 4.7),
(5, 44.9, 42.9, 4.1, 4.4),
(6, 45.0, 43.0, 3.8, 4.0),
(7, 45.0, 43.0, 3.6, 3.8),
(8, 45.0, 43.0, 3.5, 3.5),
(9, 45.0, 43.0, 3.4, 3.3),
(10, 45.0, 43.0, 3.3, 3.2);
