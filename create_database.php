<?php
/**
 * Script pour créer automatiquement la base de données et les tables
 * Exécute le contenu de setup.sql programmatiquement
 */

echo "=== CRÉATION AUTOMATIQUE DE LA BASE DE DONNÉES ===\n\n";

$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Connexion MySQL sans spécifier de base de données
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Connexion MySQL établie\n";
    
    // Créer la base de données
    echo "Création de la base de données 'reporting_tsp'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS reporting_tsp");
    echo "✓ Base de données créée\n";
    
    // Sélectionner la base de données
    $pdo->exec("USE reporting_tsp");
    echo "✓ Base de données sélectionnée\n\n";
    
    // Créer la table utilisateurs
    echo "Création de la table 'utilisateurs'...\n";
    $sql_users = "
    CREATE TABLE IF NOT EXISTS utilisateurs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(100) NOT NULL UNIQUE,
        mot_de_passe VARCHAR(255) NOT NULL,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_email (email)
    )";
    $pdo->exec($sql_users);
    echo "✓ Table 'utilisateurs' créée\n";
    
    // Créer la table qualite_tsp
    echo "Création de la table 'qualite_tsp'...\n";
    $sql_quality = "
    CREATE TABLE IF NOT EXISTS qualite_tsp (
        id INT AUTO_INCREMENT PRIMARY KEY,
        jour INT NOT NULL,
        p2o5_total DECIMAL(5,2) NOT NULL,
        p2o5_se DECIMAL(5,2) NOT NULL,
        h2o DECIMAL(5,2) NOT NULL,
        al2o3 DECIMAL(5,2) NOT NULL,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_jour (jour),
        INDEX idx_jour (jour),
        INDEX idx_date_creation (date_creation)
    )";
    $pdo->exec($sql_quality);
    echo "✓ Table 'qualite_tsp' créée\n\n";
    
    // Insérer l'utilisateur administrateur
    echo "Insertion de l'utilisateur administrateur...\n";
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO utilisateurs (email, mot_de_passe) VALUES (?, ?)");
    $stmt->execute(['<EMAIL>', $admin_password]);
    echo "✓ Utilisateur admin créé (email: <EMAIL>, mot de passe: admin123)\n";
    
    // Insérer des données de test
    echo "Insertion des données de test...\n";
    $test_data = [
        [1, 44.5, 42.5, 5.5, 5.5],
        [2, 44.6, 42.6, 5.2, 5.3],
        [3, 44.7, 42.7, 4.9, 5.1],
        [4, 44.8, 42.8, 4.5, 4.7],
        [5, 44.9, 42.9, 4.1, 4.4],
        [6, 45.0, 43.0, 3.8, 4.0],
        [7, 45.0, 43.0, 3.6, 3.8],
        [8, 45.0, 43.0, 3.5, 3.5],
        [9, 45.0, 43.0, 3.4, 3.3],
        [10, 45.0, 43.0, 3.3, 3.2]
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO qualite_tsp (jour, p2o5_total, p2o5_se, h2o, al2o3) VALUES (?, ?, ?, ?, ?)");
    foreach ($test_data as $row) {
        $stmt->execute($row);
    }
    echo "✓ Données de test insérées\n\n";
    
    echo "=== CRÉATION TERMINÉE AVEC SUCCÈS ===\n";
    echo "Vous pouvez maintenant vous connecter avec:\n";
    echo "Email: <EMAIL>\n";
    echo "Mot de passe: admin123\n\n";
    
    echo "Testez la connexion avec: php test_connection.php\n";
    
} catch (PDOException $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "\nVérifiez que:\n";
    echo "1. XAMPP MySQL est démarré\n";
    echo "2. Les paramètres de connexion sont corrects\n";
    echo "3. L'utilisateur root a les permissions nécessaires\n";
}
?>
