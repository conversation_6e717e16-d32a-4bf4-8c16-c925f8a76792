<?php
/**
 * Script pour réinitialiser le mot de passe administrateur
 * Exécuter ce script pour créer un nouveau mot de passe admin
 */

require_once 'database.php';

// Nouveau mot de passe (vous pouvez le changer)
$nouveau_mot_de_passe = 'admin123';
$email_admin = '<EMAIL>';

// Hasher le mot de passe
$mot_de_passe_hash = password_hash($nouveau_mot_de_passe, PASSWORD_DEFAULT);

try {
    // Mettre à jour le mot de passe dans la base de données
    $stmt = $pdo->prepare("UPDATE utilisateurs SET mot_de_passe = ? WHERE email = ?");
    $result = $stmt->execute([$mot_de_passe_hash, $email_admin]);
    
    if ($result) {
        echo "✓ Mot de passe administrateur mis à jour avec succès!\n\n";
        echo "=== IDENTIFIANTS DE CONNEXION ===\n";
        echo "Email: " . $email_admin . "\n";
        echo "Mot de passe: " . $nouveau_mot_de_passe . "\n";
        echo "================================\n\n";
        echo "IMPORTANT: Notez ces identifiants et supprimez ce fichier après utilisation!\n";
    } else {
        echo "❌ Erreur lors de la mise à jour du mot de passe\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur de base de données: " . $e->getMessage() . "\n";
}
