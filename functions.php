<?php
require_once 'database.php';

function verifierConnexion($email, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM utilisateurs WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['mot_de_passe'])) {
        return true;
    }
    return false;
}

function obtenirDonnees() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT * FROM qualite_tsp ORDER BY jour ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function ajouterDonnees($jour, $p2o5_total, $p2o5_se, $h2o, $al2o3) {
    global $pdo;

    // Validation des données
    if (!is_numeric($jour) || $jour <= 0) {
        throw new InvalidArgumentException("Le jour doit être un nombre positif");
    }

    $valeurs = [$p2o5_total, $p2o5_se, $h2o, $al2o3];
    foreach ($valeurs as $valeur) {
        if (!is_numeric($valeur) || $valeur < 0 || $valeur > 100) {
            throw new InvalidArgumentException("Les pourcentages doivent être entre 0 et 100");
        }
    }

    try {
        // Vérifier si le jour existe déjà
        $checkStmt = $pdo->prepare("SELECT id FROM qualite_tsp WHERE jour = ?");
        $checkStmt->execute([$jour]);
        if ($checkStmt->fetch()) {
            throw new Exception("Les données pour le jour $jour existent déjà");
        }

        $stmt = $pdo->prepare("INSERT INTO qualite_tsp (jour, p2o5_total, p2o5_se, h2o, al2o3) VALUES (?, ?, ?, ?, ?)");
        return $stmt->execute([$jour, $p2o5_total, $p2o5_se, $h2o, $al2o3]);
    } catch (PDOException $e) {
        error_log("Erreur base de données: " . $e->getMessage());
        throw new Exception("Erreur lors de l'ajout des données");
    }
}

function obtenirStatistiques() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT COUNT(*) as total, AVG(p2o5_total) as moyenne_p2o5 FROM qualite_tsp");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stmt = $pdo->query("SELECT h2o FROM qualite_tsp ORDER BY jour DESC LIMIT 1");
    $derniere_h2o = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return [
        'total' => $stats['total'],
        'moyenne_p2o5' => round($stats['moyenne_p2o5'], 1),
        'derniere_h2o' => $derniere_h2o['h2o'] ?? 0
    ];
}

function estConnecte() {
    session_start();
    return isset($_SESSION['connecte']) && $_SESSION['connecte'] === true;
}

function deconnecter() {
    session_start();
    session_destroy();
}
