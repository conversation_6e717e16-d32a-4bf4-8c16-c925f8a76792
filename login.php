<?php
require_once 'functions.php';

$erreur = '';

if (estConnecte()) {
    header('Location: dashboard.php');
    exit();
}

if ($_POST) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (verifierConnexion($email, $password)) {
        session_start();
        $_SESSION['connecte'] = true;
        $_SESSION['email'] = $email;
        header('Location: dashboard.php');
        exit();
    } else {
        $erreur = 'Email ou mot de passe incorrect';
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Reporting TSP</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-box">
            <div class="logo">TSP</div>
            <h1>Reporting TSP</h1>
            <p>Connectez-vous pour accéder au tableau de bord</p>
            
            <?php if ($erreur): ?>
                <div class="erreur"><?php echo $erreur; ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="input-group">
                    <label>Email</label>
                    <input type="email" name="email" placeholder="<EMAIL>" required>
                </div>
                
                <div class="input-group">
                    <label>Mot de passe</label>
                    <input type="password" name="password" placeholder="••••••••" required>
                </div>
                
                <button type="submit" class="btn-primary">Se connecter</button>
                
                <div class="info">
                    Compte de test: <EMAIL> / admin123
                </div>
            </form>
        </div>
    </div>
</body>
</html>
