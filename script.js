// Variables globales
let donnees = []

// Initialisation
document.addEventListener("DOMContentLoaded", () => {
  chargerDonnees()
  configurerFormulaire()
  dessinerGraphique()
})

// Charger les données
function chargerDonnees() {
  fetch("get_data.php")
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        donnees = data.donnees
        mettreAJourTableau()
        mettreAJourStats(data.stats)
        dessinerGraphique()
      }
    })
    .catch((error) => {
      console.error("Erreur:", error)
      afficherMessage("Erreur de chargement des données", "error")
    })
}

// Configurer le formulaire
function configurerFormulaire() {
  const formulaire = document.getElementById("formulaire-ajout")

  formulaire.addEventListener("submit", (e) => {
    e.preventDefault()

    const formData = new FormData(formulaire)
    const bouton = formulaire.querySelector('button[type="submit"]')
    const texteOriginal = bouton.textContent

    bouton.innerHTML = '<span class="loading"></span> Ajout...'
    bouton.disabled = true

    fetch("add_data.php", {
      method: "POST",
      body: formData,
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          afficherMessage("Données ajoutées avec succès!", "success")
          formulaire.reset()
          chargerDonnees()
        } else {
          afficherMessage(data.message, "error")
        }
      })
      .catch((error) => {
        afficherMessage("Erreur de connexion", "error")
      })
      .finally(() => {
        bouton.textContent = texteOriginal
        bouton.disabled = false
      })
  })
}

// Mettre à jour le tableau
function mettreAJourTableau() {
  const tbody = document.querySelector("#tableau-donnees tbody")
  tbody.innerHTML = ""

  donnees.forEach((ligne) => {
    const tr = document.createElement("tr")
    tr.innerHTML = `
            <td>${ligne.jour}</td>
            <td>${Number.parseFloat(ligne.p2o5_total).toFixed(1)}%</td>
            <td>${Number.parseFloat(ligne.p2o5_se).toFixed(1)}%</td>
            <td>${Number.parseFloat(ligne.h2o).toFixed(1)}%</td>
            <td>${Number.parseFloat(ligne.al2o3).toFixed(1)}%</td>
        `
    tbody.appendChild(tr)
  })
}

// Mettre à jour les statistiques
function mettreAJourStats(stats) {
  const elements = document.querySelectorAll(".stat-number")
  if (elements.length >= 3) {
    elements[0].textContent = stats.derniere_h2o + "%"
    elements[1].textContent = stats.moyenne_p2o5 + "%"
    elements[2].textContent = stats.total
  }
}

// Dessiner le graphique
function dessinerGraphique() {
  const canvas = document.getElementById("graphique")
  if (!canvas) return

  const ctx = canvas.getContext("2d")
  const width = canvas.width
  const height = canvas.height

  // Nettoyer
  ctx.clearRect(0, 0, width, height)

  if (donnees.length === 0) return

  // Marges
  const marge = { haut: 20, droite: 20, bas: 40, gauche: 50 }
  const largeurGraphique = width - marge.gauche - marge.droite
  const hauteurGraphique = height - marge.haut - marge.bas

  // Données pour le graphique
  const donneesH2O = donnees.map((d) => ({
    x: Number.parseInt(d.jour),
    y: Number.parseFloat(d.h2o),
  }))

  // Échelles
  const minX = Math.min(...donneesH2O.map((d) => d.x))
  const maxX = Math.max(...donneesH2O.map((d) => d.x))
  const minY = Math.min(...donneesH2O.map((d) => d.y)) - 0.5
  const maxY = Math.max(...donneesH2O.map((d) => d.y)) + 0.5

  function echelleLX(x) {
    return marge.gauche + ((x - minX) / (maxX - minX)) * largeurGraphique
  }

  function echelleY(y) {
    return marge.haut + ((maxY - y) / (maxY - minY)) * hauteurGraphique
  }

  // Dessiner les axes
  ctx.strokeStyle = "#ddd"
  ctx.lineWidth = 1

  // Axe X
  ctx.beginPath()
  ctx.moveTo(marge.gauche, height - marge.bas)
  ctx.lineTo(width - marge.droite, height - marge.bas)
  ctx.stroke()

  // Axe Y
  ctx.beginPath()
  ctx.moveTo(marge.gauche, marge.haut)
  ctx.lineTo(marge.gauche, height - marge.bas)
  ctx.stroke()

  // Dessiner la ligne
  ctx.strokeStyle = "#2563eb"
  ctx.lineWidth = 2
  ctx.beginPath()

  donneesH2O.forEach((point, index) => {
    const x = echelleLX(point.x)
    const y = echelleY(point.y)

    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })

  ctx.stroke()

  // Dessiner les points
  ctx.fillStyle = "#2563eb"
  donneesH2O.forEach((point) => {
    const x = echelleLX(point.x)
    const y = echelleY(point.y)

    ctx.beginPath()
    ctx.arc(x, y, 4, 0, 2 * Math.PI)
    ctx.fill()
  })

  // Labels
  ctx.fillStyle = "#333"
  ctx.font = "12px Arial"
  ctx.textAlign = "center"

  // Labels X
  for (let i = minX; i <= maxX; i++) {
    const x = echelleLX(i)
    ctx.fillText(i.toString(), x, height - marge.bas + 20)
  }

  // Labels Y
  ctx.textAlign = "right"
  const pasY = (maxY - minY) / 5
  for (let i = 0; i <= 5; i++) {
    const valeur = minY + i * pasY
    const y = echelleY(valeur)
    ctx.fillText(valeur.toFixed(1) + "%", marge.gauche - 10, y + 4)
  }
}

// Afficher un message
function afficherMessage(texte, type) {
  // Supprimer les anciens messages
  const anciens = document.querySelectorAll(".message")
  anciens.forEach((msg) => msg.remove())

  // Créer le nouveau message
  const message = document.createElement("div")
  message.className = `message ${type}`
  message.textContent = texte

  // Insérer au début du container
  const container = document.querySelector(".container")
  container.insertBefore(message, container.firstChild)

  // Supprimer après 5 secondes
  setTimeout(() => {
    message.remove()
  }, 5000)
}

// Exporter PDF
function exporterPDF() {
  window.location.href = "export_pdf.php"
}

// Exporter Excel
function exporterExcel() {
  window.location.href = "export_excel.php"
}

// Actualiser les données toutes les 30 secondes
setInterval(chargerDonnees, 30000)
