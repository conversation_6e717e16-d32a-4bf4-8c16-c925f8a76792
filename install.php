<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - Reporting TSP</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🚀 Installation - Reporting TSP</h1>

    <?php
    // Afficher un message si redirigé depuis une erreur
    if ($_GET['error'] ?? false) {
        if ($_GET['error'] === 'tables_missing') {
            echo "<div class='error'>❌ <strong>Tables manquantes détectées!</strong><br>
                  La base de données existe mais les tables ne sont pas créées.<br>
                  Cliquez sur 'Installer' ci-dessous pour les créer.</div>";
        }
    }

    if ($_POST['install'] ?? false) {
        echo "<h2>Installation en cours...</h2>";
        
        $host = 'localhost';
        $username = 'root';
        $password = '';
        $dbname = 'reporting_tsp';
        
        try {
            // 1. Test de connexion MySQL
            echo "<div class='info'>1. Test de connexion MySQL...</div>";
            $pdo = new PDO("mysql:host=$host", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "<div class='success'>✓ Connexion MySQL réussie</div>";
            
            // 2. Création de la base de données
            echo "<div class='info'>2. Création de la base de données '$dbname'...</div>";
            $pdo->exec("CREATE DATABASE IF NOT EXISTS $dbname");
            $pdo->exec("USE $dbname");
            echo "<div class='success'>✓ Base de données créée/sélectionnée</div>";
            
            // 3. Création des tables
            echo "<div class='info'>3. Création des tables...</div>";
            
            // Table utilisateurs
            $sql_users = "CREATE TABLE IF NOT EXISTS utilisateurs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(100) NOT NULL UNIQUE,
                mot_de_passe VARCHAR(255) NOT NULL,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_email (email)
            )";
            $pdo->exec($sql_users);
            echo "<div class='success'>✓ Table 'utilisateurs' créée</div>";
            
            // Table qualite_tsp
            $sql_quality = "CREATE TABLE IF NOT EXISTS qualite_tsp (
                id INT AUTO_INCREMENT PRIMARY KEY,
                jour INT NOT NULL,
                p2o5_total DECIMAL(5,2) NOT NULL,
                p2o5_se DECIMAL(5,2) NOT NULL,
                h2o DECIMAL(5,2) NOT NULL,
                al2o3 DECIMAL(5,2) NOT NULL,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_jour (jour),
                INDEX idx_jour (jour),
                INDEX idx_date_creation (date_creation)
            )";
            $pdo->exec($sql_quality);
            echo "<div class='success'>✓ Table 'qualite_tsp' créée</div>";
            
            // 4. Insertion de l'utilisateur admin
            echo "<div class='info'>4. Création de l'utilisateur administrateur...</div>";
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT IGNORE INTO utilisateurs (email, mot_de_passe) VALUES (?, ?)");
            $stmt->execute(['<EMAIL>', $admin_password]);
            echo "<div class='success'>✓ Utilisateur admin créé</div>";
            
            // 5. Insertion des données de test
            echo "<div class='info'>5. Insertion des données de test...</div>";
            $test_data = [
                [1, 44.5, 42.5, 5.5, 5.5], [2, 44.6, 42.6, 5.2, 5.3],
                [3, 44.7, 42.7, 4.9, 5.1], [4, 44.8, 42.8, 4.5, 4.7],
                [5, 44.9, 42.9, 4.1, 4.4], [6, 45.0, 43.0, 3.8, 4.0],
                [7, 45.0, 43.0, 3.6, 3.8], [8, 45.0, 43.0, 3.5, 3.5],
                [9, 45.0, 43.0, 3.4, 3.3], [10, 45.0, 43.0, 3.3, 3.2]
            ];
            
            $stmt = $pdo->prepare("INSERT IGNORE INTO qualite_tsp (jour, p2o5_total, p2o5_se, h2o, al2o3) VALUES (?, ?, ?, ?, ?)");
            foreach ($test_data as $row) {
                $stmt->execute($row);
            }
            echo "<div class='success'>✓ Données de test insérées</div>";
            
            echo "<div class='success'><h2>🎉 Installation terminée avec succès!</h2></div>";
            echo "<div class='info'>
                <h3>Identifiants de connexion:</h3>
                <strong>Email:</strong> <EMAIL><br>
                <strong>Mot de passe:</strong> admin123
            </div>";
            echo "<p><a href='login.php' class='btn'>Aller à la page de connexion</a></p>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>❌ Erreur: " . $e->getMessage() . "</div>";
            echo "<div class='info'>
                <h3>Vérifications à faire:</h3>
                <ul>
                    <li>XAMPP MySQL est-il démarré?</li>
                    <li>Le port MySQL (3306) est-il libre?</li>
                    <li>Les permissions sont-elles correctes?</li>
                </ul>
            </div>";
        }
    } else {
    ?>
    
    <div class="info">
        <h2>Avant de commencer</h2>
        <p>Assurez-vous que:</p>
        <ul>
            <li>✅ XAMPP est installé</li>
            <li>✅ MySQL est démarré dans XAMPP</li>
            <li>✅ Apache est démarré dans XAMPP</li>
        </ul>
    </div>
    
    <form method="POST">
        <input type="hidden" name="install" value="1">
        <button type="submit" class="btn">🚀 Installer la base de données</button>
    </form>
    
    <div class="info">
        <h3>Ce que fait l'installation:</h3>
        <ul>
            <li>Crée la base de données 'reporting_tsp'</li>
            <li>Crée les tables 'utilisateurs' et 'qualite_tsp'</li>
            <li>Ajoute un utilisateur admin (<EMAIL> / admin123)</li>
            <li>Insère des données de test</li>
        </ul>
    </div>
    
    <?php } ?>
</body>
</html>
