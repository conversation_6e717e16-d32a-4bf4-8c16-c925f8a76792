<?php
require_once 'functions.php';

header('Content-Type: application/json');

if (!estConnecte()) {
    echo json_encode(['success' => false, 'message' => 'Non connecté']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validation des données d'entrée
        $requiredFields = ['jour', 'p2o5_total', 'p2o5_se', 'h2o', 'al2o3'];
        foreach ($requiredFields as $field) {
            if (!isset($_POST[$field]) || $_POST[$field] === '') {
                throw new InvalidArgumentException("Le champ $field est requis");
            }
        }

        $jour = intval($_POST['jour']);
        $p2o5_total = floatval($_POST['p2o5_total']);
        $p2o5_se = floatval($_POST['p2o5_se']);
        $h2o = floatval($_POST['h2o']);
        $al2o3 = floatval($_POST['al2o3']);

        if (ajouterDonnees($jour, $p2o5_total, $p2o5_se, $h2o, $al2o3)) {
            echo json_encode(['success' => true, 'message' => 'Données ajoutées avec succès']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'ajout des données']);
        }
    } catch (InvalidArgumentException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
}
