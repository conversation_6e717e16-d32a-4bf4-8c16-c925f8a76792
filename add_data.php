<?php
require_once 'functions.php';

header('Content-Type: application/json');

if (!estConnecte()) {
    echo json_encode(['success' => false, 'message' => 'Non connecté']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $jour = intval($_POST['jour']);
    $p2o5_total = floatval($_POST['p2o5_total']);
    $p2o5_se = floatval($_POST['p2o5_se']);
    $h2o = floatval($_POST['h2o']);
    $al2o3 = floatval($_POST['al2o3']);
    
    if (ajouterDonnees($jour, $p2o5_total, $p2o5_se, $h2o, $al2o3)) {
        echo json_encode(['success' => true, 'message' => 'Données ajoutées']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Erreur ajout']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
}
?>
