<?php
$host = 'localhost';
$dbname = 'reporting_tsp';
$username = 'root';
$password = '';

try {
    // D'abord, tester la connexion MySQL de base
    $pdo_test = new PDO("mysql:host=$host", $username, $password);
    $pdo_test->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Vérifier si la base de données existe
    $stmt = $pdo_test->query("SHOW DATABASES LIKE '$dbname'");
    if (!$stmt->fetch()) {
        // La base de données n'existe pas, la créer
        $pdo_test->exec("CREATE DATABASE $dbname");
    }

    // Maintenant se connecter à la base de données spécifique
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);

} catch(PDOException $e) {
    // Message d'erreur plus détaillé pour le débogage
    $error_msg = "Erreur de connexion à la base de données: " . $e->getMessage();
    error_log($error_msg);

    // Afficher un message différent selon le type d'erreur
    if (strpos($e->getMessage(), 'Connection refused') !== false ||
        strpos($e->getMessage(), 'Can\'t connect') !== false) {
        die("❌ Impossible de se connecter à MySQL. Vérifiez que XAMPP MySQL est démarré.");
    } else {
        die("❌ Erreur de base de données: " . $e->getMessage() . "<br><br>
             <strong>Solutions possibles:</strong><br>
             1. Vérifiez que XAMPP MySQL est démarré<br>
             2. Exécutez: <code>php create_database.php</code><br>
             3. Ou importez setup.sql dans phpMyAdmin");
    }
}
