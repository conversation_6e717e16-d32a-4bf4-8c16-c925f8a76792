/* Variables CSS pour les couleurs OCP */
:root {
  --ocp-green-primary: #00A651;
  --ocp-green-dark: #008B44;
  --ocp-green-light: #4CBB7A;
  --ocp-green-pale: #E8F5E8;
  --ocp-gray-dark: #2C3E50;
  --ocp-gray-medium: #7F8C8D;
  --ocp-gray-light: #ECF0F1;
  --ocp-white: #FFFFFF;
  --ocp-shadow: rgba(0, 166, 81, 0.15);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--ocp-gray-light);
  color: var(--ocp-gray-dark);
  line-height: 1.6;
}

/* Page de connexion */
.login-page {
  background: linear-gradient(135deg, var(--ocp-green-primary) 0%, var(--ocp-green-dark) 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Logo OCP en arrière-plan */
.login-page::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
  height: 800px;
  background-image: url('ocp-logo.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.08;
  z-index: 1;
  animation: rotate 60s linear infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.login-container {
  padding: 20px;
  position: relative;
  z-index: 2;
}

.login-box {
  background: var(--ocp-white);
  padding: 50px 40px;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 25px var(--ocp-shadow);
  text-align: center;
  max-width: 450px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--ocp-green-primary), var(--ocp-green-dark));
  color: var(--ocp-white);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: bold;
  margin: 0 auto 25px;
  box-shadow: 0 8px 25px var(--ocp-shadow);
  position: relative;
}

.logo::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--ocp-green-light), var(--ocp-green-primary));
  border-radius: 22px;
  z-index: -1;
  opacity: 0.3;
}

.login-box h1 {
  margin-bottom: 15px;
  color: var(--ocp-gray-dark);
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.login-box p {
  color: var(--ocp-gray-medium);
  margin-bottom: 35px;
  font-size: 16px;
  line-height: 1.5;
}

.input-group {
  margin-bottom: 25px;
  text-align: left;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--ocp-gray-dark);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.input-group input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid var(--ocp-gray-light);
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: var(--ocp-white);
}

.input-group input:focus {
  outline: none;
  border-color: var(--ocp-green-primary);
  box-shadow: 0 0 0 4px var(--ocp-shadow);
  transform: translateY(-1px);
}

.input-group input::placeholder {
  color: var(--ocp-gray-medium);
}

.btn-primary {
  width: 100%;
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--ocp-green-primary), var(--ocp-green-dark));
  color: var(--ocp-white);
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 25px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px var(--ocp-shadow);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--ocp-green-dark), var(--ocp-green-primary));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--ocp-shadow);
}

.btn-primary:active {
  transform: translateY(0);
}

.info {
  background: var(--ocp-green-pale);
  color: var(--ocp-green-dark);
  padding: 18px;
  border-radius: 12px;
  font-size: 14px;
  border-left: 4px solid var(--ocp-green-primary);
}

.erreur {
  background: #FEE2E2;
  color: #DC2626;
  padding: 18px;
  border-radius: 12px;
  margin-bottom: 25px;
  border-left: 4px solid #DC2626;
  font-weight: 500;
}

/* Navigation */
.navbar {
  background: var(--ocp-white);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 0 30px;
  border-bottom: 3px solid var(--ocp-green-primary);
}

.nav-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 700;
  color: var(--ocp-gray-dark);
}

.nav-brand .logo {
  width: 50px;
  height: 50px;
  font-size: 18px;
  margin-right: 15px;
  background: linear-gradient(135deg, var(--ocp-green-primary), var(--ocp-green-dark));
}

.btn-logout {
  background: linear-gradient(135deg, #DC2626, #B91C1C);
  color: var(--ocp-white);
  padding: 12px 24px;
  text-decoration: none;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
}

.btn-logout:hover {
  background: linear-gradient(135deg, #B91C1C, #DC2626);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

/* Container principal */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 30px;
  background: var(--ocp-gray-light);
  min-height: calc(100vh - 70px);
}

.header {
  margin-bottom: 40px;
  text-align: center;
  padding: 30px 0;
}

.header h1 {
  color: var(--ocp-gray-dark);
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 10px;
  letter-spacing: -1px;
}

.header p {
  color: var(--ocp-gray-medium);
  font-size: 18px;
  max-width: 600px;
  margin: 0 auto;
}

/* Statistiques */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background: var(--ocp-white);
  padding: 30px 25px;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  text-align: center;
  border: 1px solid rgba(0, 166, 81, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--ocp-green-primary), var(--ocp-green-light));
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 166, 81, 0.15);
}

.stat-number {
  font-size: 48px;
  font-weight: 700;
  color: var(--ocp-green-primary);
  margin-bottom: 10px;
  line-height: 1;
}

.stat-label {
  color: var(--ocp-gray-medium);
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-export {
  background: linear-gradient(135deg, var(--ocp-green-primary), var(--ocp-green-dark));
  color: var(--ocp-white);
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  margin: 0 8px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-export:hover {
  background: linear-gradient(135deg, var(--ocp-green-dark), var(--ocp-green-primary));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--ocp-shadow);
}

/* Cards */
.card {
  background: var(--ocp-white);
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  margin-bottom: 40px;
  overflow: hidden;
  border: 1px solid rgba(0, 166, 81, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(0, 166, 81, 0.12);
}

.card-header {
  padding: 25px 30px;
  border-bottom: 2px solid var(--ocp-green-pale);
  background: linear-gradient(135deg, var(--ocp-green-pale), rgba(255, 255, 255, 0.8));
}

.card-header h2 {
  font-size: 24px;
  color: var(--ocp-gray-dark);
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
}

.card-header h2::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, var(--ocp-green-primary), var(--ocp-green-dark));
  margin-right: 15px;
  border-radius: 2px;
}

.card-content {
  padding: 30px;
}

/* Formulaire */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

/* Tableau */
.table-container {
  overflow-x: auto;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

table {
  width: 100%;
  border-collapse: collapse;
  background: var(--ocp-white);
}

th,
td {
  padding: 18px 20px;
  text-align: left;
  border-bottom: 1px solid var(--ocp-gray-light);
}

th {
  background: linear-gradient(135deg, var(--ocp-green-pale), rgba(255, 255, 255, 0.9));
  font-weight: 700;
  color: var(--ocp-gray-dark);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
  position: sticky;
  top: 0;
  z-index: 10;
}

td {
  color: var(--ocp-gray-dark);
  font-weight: 500;
}

tr:hover {
  background: var(--ocp-green-pale);
  transform: scale(1.01);
  transition: all 0.2s ease;
}

tr:first-child th:first-child {
  border-top-left-radius: 15px;
}

tr:first-child th:last-child {
  border-top-right-radius: 15px;
}

/* Graphique */
#graphique {
  width: 100%;
  height: 400px;
  border: 2px solid var(--ocp-gray-light);
  border-radius: 15px;
  background: var(--ocp-white);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

/* Messages */
.message {
  padding: 20px 25px;
  border-radius: 15px;
  margin-bottom: 25px;
  font-weight: 500;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.message::before {
  content: '';
  width: 20px;
  height: 20px;
  margin-right: 15px;
  border-radius: 50%;
}

.message.success {
  background: linear-gradient(135deg, var(--ocp-green-pale), rgba(255, 255, 255, 0.9));
  color: var(--ocp-green-dark);
  border-left: 4px solid var(--ocp-green-primary);
}

.message.success::before {
  background: var(--ocp-green-primary);
}

.message.error {
  background: linear-gradient(135deg, #FEE2E2, rgba(255, 255, 255, 0.9));
  color: #DC2626;
  border-left: 4px solid #DC2626;
}

.message.error::before {
  background: #DC2626;
}

/* Loading */
.loading {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 3px solid var(--ocp-gray-light);
  border-top: 3px solid var(--ocp-green-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animations supplémentaires */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.card {
  animation: fadeIn 0.6s ease-out;
}

.stat-card {
  animation: fadeIn 0.6s ease-out;
}

.message {
  animation: slideIn 0.4s ease-out;
}

/* Responsive */
@media (max-width: 1200px) {
  .container {
    padding: 20px;
  }

  .nav-content {
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header h1 {
    font-size: 32px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .nav-content {
    padding: 0 15px;
    height: 60px;
  }

  .nav-brand {
    font-size: 20px;
  }

  .nav-brand .logo {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .card-content {
    padding: 20px;
  }

  .card-header {
    padding: 20px;
  }

  .login-box {
    margin: 20px;
    padding: 30px 25px;
  }

  .btn-logout {
    padding: 10px 16px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .header {
    padding: 20px 0;
  }

  .header h1 {
    font-size: 28px;
  }

  .stat-card {
    padding: 20px 15px;
  }

  .stat-number {
    font-size: 36px;
  }

  .card-content {
    padding: 15px;
  }

  .card-header {
    padding: 15px;
  }

  .login-box {
    padding: 25px 20px;
  }

  th, td {
    padding: 12px 10px;
    font-size: 14px;
  }
}
