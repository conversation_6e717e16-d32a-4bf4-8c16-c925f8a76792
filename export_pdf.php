<?php
require_once 'functions.php';

if (!estConnecte()) {
    header('Location: login.php');
    exit();
}

$donnees = obtenirDonnees();

$contenu = "RAPPORT QUALITÉ TSP\n";
$contenu .= "==================\n\n";
$contenu .= "Date: " . date('d/m/Y H:i') . "\n\n";

foreach ($donnees as $ligne) {
    $contenu .= "Jour {$ligne['jour']}: ";
    $contenu .= "P₂O₅ Total: {$ligne['p2o5_total']}%, ";
    $contenu .= "P₂O₅ SE: {$ligne['p2o5_se']}%, ";
    $contenu .= "H₂O: {$ligne['h2o']}%, ";
    $contenu .= "Al₂O₃: {$ligne['al2o3']}%\n";
}

header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="rapport-tsp.txt"');
echo $contenu;
?>
