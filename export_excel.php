<?php
require_once 'functions.php';

if (!estConnecte()) {
    header('Location: login.php');
    exit();
}

$donnees = obtenirDonnees();

$csv = "Jour,P₂O₅ Total,P₂O₅ SE,H₂O,Al₂O₃\n";

foreach ($donnees as $ligne) {
    $csv .= "{$ligne['jour']},{$ligne['p2o5_total']},{$ligne['p2o5_se']},{$ligne['h2o']},{$ligne['al2o3']}\n";
}

header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="donnees-tsp.csv"');
echo $csv;
?>
