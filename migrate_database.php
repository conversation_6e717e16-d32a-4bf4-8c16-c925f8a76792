<?php
/**
 * Script de migration pour mettre à jour la structure de la base de données
 * Exécuter ce script une seule fois pour appliquer les améliorations
 */

require_once 'database.php';

echo "Début de la migration de la base de données...\n";

try {
    // 1. Ajouter l'index unique sur l'email si il n'existe pas
    $pdo->exec("ALTER TABLE utilisateurs ADD UNIQUE INDEX idx_email_unique (email)");
    echo "✓ Index unique ajouté sur l'email des utilisateurs\n";
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
        echo "- Index unique sur l'email existe déjà\n";
    } else {
        echo "Erreur lors de l'ajout de l'index unique sur l'email: " . $e->getMessage() . "\n";
    }
}

try {
    // 2. Ajouter la colonne date_creation aux utilisateurs si elle n'existe pas
    $pdo->exec("ALTER TABLE utilisateurs ADD COLUMN date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
    echo "✓ Colonne date_creation ajoutée à la table utilisateurs\n";
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "- Colonne date_creation existe déjà dans utilisateurs\n";
    } else {
        echo "Erreur lors de l'ajout de la colonne date_creation: " . $e->getMessage() . "\n";
    }
}

try {
    // 3. Modifier les types de données FLOAT vers DECIMAL pour plus de précision
    $pdo->exec("ALTER TABLE qualite_tsp 
                MODIFY COLUMN p2o5_total DECIMAL(5,2) NOT NULL,
                MODIFY COLUMN p2o5_se DECIMAL(5,2) NOT NULL,
                MODIFY COLUMN h2o DECIMAL(5,2) NOT NULL,
                MODIFY COLUMN al2o3 DECIMAL(5,2) NOT NULL");
    echo "✓ Types de données modifiés de FLOAT vers DECIMAL\n";
} catch (PDOException $e) {
    echo "Erreur lors de la modification des types de données: " . $e->getMessage() . "\n";
}

try {
    // 4. Ajouter l'index unique sur le jour
    $pdo->exec("ALTER TABLE qualite_tsp ADD UNIQUE INDEX idx_jour_unique (jour)");
    echo "✓ Index unique ajouté sur le jour\n";
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
        echo "- Index unique sur le jour existe déjà\n";
    } else {
        echo "Erreur lors de l'ajout de l'index unique sur le jour: " . $e->getMessage() . "\n";
    }
}

try {
    // 5. Ajouter des index pour améliorer les performances
    $pdo->exec("ALTER TABLE qualite_tsp ADD INDEX idx_date_creation (date_creation)");
    echo "✓ Index ajouté sur date_creation\n";
} catch (PDOException $e) {
    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
        echo "- Index sur date_creation existe déjà\n";
    } else {
        echo "Erreur lors de l'ajout de l'index sur date_creation: " . $e->getMessage() . "\n";
    }
}

echo "\nMigration terminée avec succès!\n";
echo "Vous pouvez maintenant supprimer ce fichier migrate_database.php\n";
