<?php
require_once 'functions.php';

if (!estConnecte()) {
    header('Location: login.php');
    exit();
}

$donnees = obtenirDonnees();
$stats = obtenirStatistiques();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Reporting TSP</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <div class="nav-brand">
                <div class="logo">TSP</div>
                <span>Reporting TSP</span>
            </div>
            <a href="logout.php" class="btn-logout">Déconnexion</a>
        </div>
    </nav>

    <div class="container">
        <!-- Titre -->
        <div class="header">
            <h1>Dashboard TSP</h1>
            <p>Suivi de la qualité de production d'engrais TSP</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['derniere_h2o']; ?>%</div>
                <div class="stat-label">Dernière H₂O</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['moyenne_p2o5']; ?>%</div>
                <div class="stat-label">P₂O₅ Moyen</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Mesures</div>
            </div>
            <div class="stat-card">
                <button onclick="exporterPDF()" class="btn-export">PDF</button>
                <button onclick="exporterExcel()" class="btn-export">Excel</button>
                <div class="stat-label">Exports</div>
            </div>
        </div>

        <!-- Graphique -->
        <div class="card">
            <div class="card-header">
                <h2>Évolution du % H₂O</h2>
            </div>
            <div class="card-content">
                <canvas id="graphique" width="800" height="300"></canvas>
            </div>
        </div>

        <!-- Formulaire d'ajout -->
        <div class="card">
            <div class="card-header">
                <h2>Ajouter des Données</h2>
            </div>
            <div class="card-content">
                <form id="formulaire-ajout">
                    <div class="form-grid">
                        <div class="input-group">
                            <label>Jour</label>
                            <input type="number" name="jour" min="1" required>
                        </div>
                        <div class="input-group">
                            <label>% P₂O₅ Total</label>
                            <input type="number" name="p2o5_total" step="0.1" min="0" required>
                        </div>
                        <div class="input-group">
                            <label>% P₂O₅ SE</label>
                            <input type="number" name="p2o5_se" step="0.1" min="0" required>
                        </div>
                        <div class="input-group">
                            <label>% H₂O</label>
                            <input type="number" name="h2o" step="0.1" min="0" required>
                        </div>
                        <div class="input-group">
                            <label>% Al₂O₃</label>
                            <input type="number" name="al2o3" step="0.1" min="0" required>
                        </div>
                    </div>
                    <button type="submit" class="btn-primary">Ajouter</button>
                </form>
            </div>
        </div>

        <!-- Tableau -->
        <div class="card">
            <div class="card-header">
                <h2>Données de Qualité</h2>
            </div>
            <div class="card-content">
                <div class="table-container">
                    <table id="tableau-donnees">
                        <thead>
                            <tr>
                                <th>Jour</th>
                                <th>% P₂O₅ Total</th>
                                <th>% P₂O₅ SE</th>
                                <th>% H₂O</th>
                                <th>% Al₂O₃</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($donnees as $ligne): ?>
                            <tr>
                                <td><?php echo $ligne['jour']; ?></td>
                                <td><?php echo number_format($ligne['p2o5_total'], 1); ?>%</td>
                                <td><?php echo number_format($ligne['p2o5_se'], 1); ?>%</td>
                                <td><?php echo number_format($ligne['h2o'], 1); ?>%</td>
                                <td><?php echo number_format($ligne['al2o3'], 1); ?>%</td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
